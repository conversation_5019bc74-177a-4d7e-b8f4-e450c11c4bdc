/**
 * 图片对齐方式功能测试
 */

import type { Board } from "../Board"
import {
  type ImageCropPosition,
  ImageDrawer,
} from "../shape/drawers/ImageDrawer"
import type { ImageModel } from "../shape/models/ImageModel"

/**
 * 测试图片对齐方式功能
 */
export class ImageCropTest {
  private imageDrawer: ImageDrawer
  private testImageUrl = "https://konvajs.org/assets/darth-vader.jpg"

  constructor(board: Board) {
    this.imageDrawer = new ImageDrawer(board)
  }

  /**
   * 测试基本的图片插入和对齐方式设置
   */
  async testBasicCropPosition() {
    console.log("🧪 测试基本对齐方式功能...")

    try {
      // 测试默认对齐方式
      const image1 = await this.imageDrawer.insert(this.testImageUrl, {
        x: 50,
        y: 50,
        width: 200,
        height: 150,
      })

      console.log("✅ 默认对齐方式图片创建成功")
      console.log("   对齐方式:", image1.getCropPosition())

      // 测试指定对齐方式
      const image2 = await this.imageDrawer.insert(this.testImageUrl, {
        cropPosition: "left-top",
        x: 300,
        y: 50,
        width: 200,
        height: 150,
      })

      console.log("✅ 左上角对齐图片创建成功")
      console.log("   对齐方式:", image2.getCropPosition())

      return { image1, image2 }
    } catch (error) {
      console.error("❌ 基本对齐方式测试失败:", error)
      throw error
    }
  }

  /**
   * 测试动态更改对齐方式
   */
  async testDynamicCropPosition() {
    console.log("🧪 测试动态更改对齐方式...")

    try {
      const image = await this.imageDrawer.insert(this.testImageUrl, {
        cropPosition: "center-middle",
        x: 100,
        y: 100,
        width: 200,
        height: 150,
      })

      console.log("✅ 初始图片创建成功，对齐方式:", image.getCropPosition())

      // 更改对齐方式
      const positions: ImageCropPosition[] = [
        "left-top",
        "right-top",
        "right-bottom",
        "left-bottom",
      ]

      for (const position of positions) {
        image.setCropPosition(position)
        console.log(`✅ 对齐方式已更改为: ${position}`)
        console.log("   当前对齐方式:", image.getCropPosition())

        // 模拟等待以便观察变化
        await new Promise((resolve) => setTimeout(resolve, 500))
      }

      return image
    } catch (error) {
      console.error("❌ 动态对齐方式测试失败:", error)
      throw error
    }
  }

  /**
   * 测试所有对齐方式
   */
  async testAllCropPositions() {
    console.log("🧪 测试所有对齐方式...")

    const positions: ImageCropPosition[] = [
      "left-top",
      "center-top",
      "right-top",
      "left-middle",
      "center-middle",
      "right-middle",
      "left-bottom",
      "center-bottom",
      "right-bottom",
    ]

    const images: ImageModel[] = []

    try {
      for (let i = 0; i < positions.length; i++) {
        const position = positions[i]
        const row = Math.floor(i / 3)
        const col = i % 3

        const image = await this.imageDrawer.insert(this.testImageUrl, {
          cropPosition: position,
          x: col * 220 + 50,
          y: row * 180 + 50,
          width: 200,
          height: 150,
        })

        images.push(image)
        console.log(`✅ ${position} 对齐方式图片创建成功`)
      }

      console.log(`✅ 所有 ${positions.length} 种对齐方式测试完成`)
      return images
    } catch (error) {
      console.error("❌ 所有对齐方式测试失败:", error)
      throw error
    }
  }

  /**
   * 运行所有测试
   */
  async runAllTests() {
    console.log("🚀 开始图片对齐方式功能测试...")

    try {
      // 基本功能测试
      await this.testBasicCropPosition()
      console.log("")

      // 动态更改测试
      await this.testDynamicCropPosition()
      console.log("")

      // 所有对齐方式测试
      await this.testAllCropPositions()
      console.log("")

      console.log("🎉 所有测试完成！")
    } catch (error) {
      console.error("💥 测试过程中出现错误:", error)
    }
  }
}

/**
 * 创建测试实例的工厂函数
 */
export function createImageCropTest(board: Board) {
  return new ImageCropTest(board)
}

/**
 * 快速测试函数
 */
export async function quickTest(board: Board) {
  const test = createImageCropTest(board)
  await test.runAllTests()
}
