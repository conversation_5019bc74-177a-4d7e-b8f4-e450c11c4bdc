/**
 * 图片对齐方式示例
 * 
 * 这个示例展示了如何使用修改后的 ImageDrawer 来设置图片的对齐方式
 */

import { ImageDrawer, ImageCropPosition } from '../shape/drawers/ImageDrawer'
import type { Board } from '../Board'

/**
 * 图片对齐方式示例类
 */
export class ImageCropExample {
  private board: Board
  private imageDrawer: ImageDrawer

  constructor(board: Board) {
    this.board = board
    this.imageDrawer = new ImageDrawer(board)
  }

  /**
   * 插入图片并设置对齐方式
   * 
   * @param imageUrl 图片URL
   * @param cropPosition 对齐方式
   */
  async insertImageWithCropPosition(
    imageUrl: string, 
    cropPosition: ImageCropPosition = 'center-middle'
  ) {
    try {
      const imageModel = await this.imageDrawer.insert(imageUrl, {
        cropPosition,
        x: 100,
        y: 100,
        width: 300,
        height: 200
      })

      console.log(`图片已插入，对齐方式: ${cropPosition}`)
      return imageModel
    } catch (error) {
      console.error('插入图片失败:', error)
      throw error
    }
  }

  /**
   * 演示所有对齐方式
   * 
   * @param imageUrl 图片URL
   */
  async demonstrateAllCropPositions(imageUrl: string) {
    const positions: ImageCropPosition[] = [
      'left-top', 'center-top', 'right-top',
      'left-middle', 'center-middle', 'right-middle',
      'left-bottom', 'center-bottom', 'right-bottom'
    ]

    const images = []
    
    for (let i = 0; i < positions.length; i++) {
      const position = positions[i]
      const row = Math.floor(i / 3)
      const col = i % 3
      
      try {
        const imageModel = await this.imageDrawer.insert(imageUrl, {
          cropPosition: position,
          x: col * 320 + 50,
          y: row * 220 + 50,
          width: 300,
          height: 200
        })
        
        images.push(imageModel)
        console.log(`已创建图片，位置: ${position}`)
      } catch (error) {
        console.error(`创建图片失败，位置: ${position}`, error)
      }
    }

    return images
  }

  /**
   * 动态更改图片对齐方式
   * 
   * @param imageModel 图片模型
   * @param newPosition 新的对齐方式
   */
  changeCropPosition(imageModel: any, newPosition: ImageCropPosition) {
    if (imageModel && typeof imageModel.setCropPosition === 'function') {
      imageModel.setCropPosition(newPosition)
      console.log(`图片对齐方式已更改为: ${newPosition}`)
    } else {
      console.error('无效的图片模型或不支持设置对齐方式')
    }
  }
}

/**
 * 使用示例
 */
export function createImageCropExample(board: Board) {
  const example = new ImageCropExample(board)
  
  return {
    // 插入单个图片
    insertImage: (url: string, position: ImageCropPosition) => 
      example.insertImageWithCropPosition(url, position),
    
    // 演示所有对齐方式
    demonstrateAll: (url: string) => 
      example.demonstrateAllCropPositions(url),
    
    // 更改对齐方式
    changePosition: (imageModel: any, position: ImageCropPosition) => 
      example.changeCropPosition(imageModel, position)
  }
}
