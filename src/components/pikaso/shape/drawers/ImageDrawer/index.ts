import Konva from "konva"

import { createImageFromUrl } from "../../../utils/create-image-from-url"
import { isBrowser } from "../../../utils/detect-environment"
import { imageToDataUrl } from "../../../utils/image-to-url"

import type { Board } from "../../../Board"
import { ImageModel } from "../../models/ImageModel"

// 图片对齐方式类型定义
export type ImageCropPosition =
  | "left-top"
  | "center-top"
  | "right-top"
  | "left-middle"
  | "center-middle"
  | "right-middle"
  | "left-bottom"
  | "center-bottom"
  | "right-bottom"

// 扩展 ImageConfig 接口以支持对齐方式
export interface ExtendedImageConfig extends Partial<Konva.ImageConfig> {
  cropPosition?: ImageCropPosition
}

export class ImageDrawer {
  /**
   * Represents the [[Board]]
   */
  private board: Board

  /**
   * Creates a new image builder component
   *
   * @param board The [[Board]
   */
  constructor(board: Board) {
    this.board = board
  }

  /**
   * 计算图片裁剪参数，保持比例不变
   *
   * @param image 图片实例
   * @param size 目标尺寸
   * @param clipPosition 对齐方式
   * @returns 裁剪参数
   */
  private getCrop(
    image: HTMLImageElement,
    size: { width: number; height: number },
    clipPosition: ImageCropPosition = "center-middle",
  ) {
    const width = size.width
    const height = size.height
    const aspectRatio = width / height

    let newWidth: number
    let newHeight: number

    const imageRatio = image.width / image.height

    if (aspectRatio >= imageRatio) {
      newWidth = image.width
      newHeight = image.width / aspectRatio
    } else {
      newWidth = image.height * aspectRatio
      newHeight = image.height
    }

    let x = 0
    let y = 0

    if (clipPosition === "left-top") {
      x = 0
      y = 0
    } else if (clipPosition === "left-middle") {
      x = 0
      y = (image.height - newHeight) / 2
    } else if (clipPosition === "left-bottom") {
      x = 0
      y = image.height - newHeight
    } else if (clipPosition === "center-top") {
      x = (image.width - newWidth) / 2
      y = 0
    } else if (clipPosition === "center-middle") {
      x = (image.width - newWidth) / 2
      y = (image.height - newHeight) / 2
    } else if (clipPosition === "center-bottom") {
      x = (image.width - newWidth) / 2
      y = image.height - newHeight
    } else if (clipPosition === "right-top") {
      x = image.width - newWidth
      y = 0
    } else if (clipPosition === "right-middle") {
      x = image.width - newWidth
      y = (image.height - newHeight) / 2
    } else if (clipPosition === "right-bottom") {
      x = image.width - newWidth
      y = image.height - newHeight
    }

    return {
      cropX: x,
      cropY: y,
      cropWidth: newWidth,
      cropHeight: newHeight,
    }
  }

  /**
   * 应用裁剪到图片实例
   *
   * @param img Konva 图片实例
   * @param pos 对齐方式
   */
  private applyCrop(img: Konva.Image, pos: ImageCropPosition) {
    img.setAttr("lastCropUsed", pos)
    const imageElement = img.image() as HTMLImageElement

    if (!imageElement) return

    const crop = this.getCrop(
      imageElement,
      { width: img.width(), height: img.height() },
      pos,
    )
    img.setAttrs(crop)
  }

  /**
   * Inserts a new image into the board
   *
   * @param image The image [[File]]
   * @param config The image node configuration
   */
  public async insert(
    image: File | Konva.Image | string,
    config: ExtendedImageConfig = {},
  ): Promise<ImageModel> {
    let imageInstance: Konva.Image

    if (image instanceof Konva.Image) {
      imageInstance = image
    } else {
      const url =
        isBrowser() && image instanceof File
          ? await imageToDataUrl(image)
          : image

      imageInstance = await createImageFromUrl(url as string)
    }

    const ratio = imageInstance.width() / imageInstance.height()
    const defaultHeight = this.board.stage.height() / 2
    const defaultWidth = defaultHeight * ratio

    // 提取对齐方式配置
    const { cropPosition = "center-middle", ...konvaConfig } = config

    imageInstance.setAttrs({
      width: defaultWidth,
      height: defaultHeight,
      x: (this.board.stage.width() - defaultWidth) / 2,
      y: (this.board.stage.height() - defaultHeight) / 2,
      rotation: this.board.stage.rotation() * -1,
      draggable: this.board.settings.selection?.interactive,
      ...konvaConfig,
    })

    // 应用初始裁剪
    this.applyCrop(imageInstance, cropPosition)

    const imageModel = new ImageModel(this.board, imageInstance, {
      transformer: {
        keepRatio: false, // 允许自由变换，但保持裁剪比例
        enabledAnchors: [
          "top-left",
          "top-right",
          "bottom-left",
          "bottom-right",
        ],
      },
    })

    // 设置对齐方式到模型中
    imageModel.setCropPosition(cropPosition)

    return imageModel
  }
}
