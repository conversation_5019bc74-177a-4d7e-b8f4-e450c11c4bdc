import type Konva from "konva"

import type { Board } from "../../../Board"
import { rotateAroundCenter } from "../../../utils/rotate-around-center"

import type { ShapeConfig } from "../../../types"
import { ShapeModel } from "../../ShapeModel"
import type { ImageCropPosition } from "../../drawers/ImageDrawer"

export class ImageModel extends ShapeModel<Konva.Image, Konva.ImageConfig> {
  /**
   * 当前图片的对齐方式
   */
  private cropPosition: ImageCropPosition = "center-middle"

  /**
   * 创建一个新的图像模型
   *
   * @param board 画板实例
   * @param node 图像节点
   * @param config 配置选项
   */
  constructor(board: Board, node: Konva.Image, config: ShapeConfig = {}) {
    super(board, node, config)

    // 绑定双击事件
    node.on("dblclick", this.startCropping.bind(this))

    // 添加移动端双击支持
    let lastTap = 0
    node.on("touchend", () => {
      const currentTime = new Date().getTime()
      const tapLength = currentTime - lastTap
      if (tapLength < 300 && tapLength > 0) {
        // 对于触摸事件，直接调用裁剪逻辑而不传递事件对象
        this.handleDoubleTap()
      }
      lastTap = currentTime
    })

    // 绑定变换事件，保持裁剪比例
    node.on("transform", this.onTransform.bind(this))
  }

  /**
   * @inheritdoc
   */
  public get type(): string {
    return "image"
  }

  /**
   * @inheritdoc
   * @override
   */
  public rotate(theta: number) {
    rotateAroundCenter(this.node, theta)

    this.board.events.emit("shape:rotate", {
      shapes: [this],
    })
  }

  /**
   * 设置图片的对齐方式
   *
   * @param position 对齐方式
   */
  public setCropPosition(position: ImageCropPosition) {
    this.cropPosition = position
    this.node.setAttr("lastCropUsed", position)
  }

  /**
   * 获取当前的对齐方式
   *
   * @returns 当前对齐方式
   */
  public getCropPosition(): ImageCropPosition {
    return this.cropPosition
  }

  /**
   * 变换事件处理，保持裁剪比例
   */
  private onTransform() {
    // 重置缩放，保持宽高比例
    this.node.setAttrs({
      scaleX: 1,
      scaleY: 1,
      width: this.node.width() * this.node.scaleX(),
      height: this.node.height() * this.node.scaleY(),
    })

    // 重新应用裁剪
    this.applyCropToSelf()
  }

  /**
   * 应用裁剪到自身
   */
  private applyCropToSelf() {
    const imageElement = this.node.image() as HTMLImageElement
    if (!imageElement) return

    const crop = this.getCrop(
      imageElement,
      { width: this.node.width(), height: this.node.height() },
      this.cropPosition,
    )
    this.node.setAttrs(crop)
  }

  /**
   * 计算图片裁剪参数，保持比例不变
   */
  private getCrop(
    image: HTMLImageElement,
    size: { width: number; height: number },
    clipPosition: ImageCropPosition = "center-middle",
  ) {
    const width = size.width
    const height = size.height
    const aspectRatio = width / height

    let newWidth: number
    let newHeight: number

    const imageRatio = image.width / image.height

    if (aspectRatio >= imageRatio) {
      newWidth = image.width
      newHeight = image.width / aspectRatio
    } else {
      newWidth = image.height * aspectRatio
      newHeight = image.height
    }

    let x = 0
    let y = 0

    if (clipPosition === "left-top") {
      x = 0
      y = 0
    } else if (clipPosition === "left-middle") {
      x = 0
      y = (image.height - newHeight) / 2
    } else if (clipPosition === "left-bottom") {
      x = 0
      y = image.height - newHeight
    } else if (clipPosition === "center-top") {
      x = (image.width - newWidth) / 2
      y = 0
    } else if (clipPosition === "center-middle") {
      x = (image.width - newWidth) / 2
      y = (image.height - newHeight) / 2
    } else if (clipPosition === "center-bottom") {
      x = (image.width - newWidth) / 2
      y = image.height - newHeight
    } else if (clipPosition === "right-top") {
      x = image.width - newWidth
      y = 0
    } else if (clipPosition === "right-middle") {
      x = image.width - newWidth
      y = (image.height - newHeight) / 2
    } else if (clipPosition === "right-bottom") {
      x = image.width - newWidth
      y = image.height - newHeight
    }

    return {
      cropX: x,
      cropY: y,
      cropWidth: newWidth,
      cropHeight: newHeight,
    }
  }

  /**
   * 处理双击事件（触摸和鼠标）
   */
  private handleDoubleTap() {
    console.log("handleDoubleTap")
    // 这里可以添加双击后的逻辑，比如切换对齐方式或打开设置面板
    // 暂时只记录日志
  }

  private startCropping(e: Konva.KonvaEventObject<MouseEvent>) {
    console.log("startCropping")
    e.cancelBubble = true
    this.handleDoubleTap()
  }
}
