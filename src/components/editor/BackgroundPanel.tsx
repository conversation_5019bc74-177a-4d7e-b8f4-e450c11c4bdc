import { ColorPicker } from "@/components/ColorPicker"
import { But<PERSON> } from "@/components/ui/button"
import { Image as ImageIcon, Palette, Upload, X } from "lucide-react"
import { useRef, useState } from "react"
import { Drawer } from "vaul"

interface BackgroundPanelProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  onBackgroundChange: (type: "color" | "image", value: string) => void
}

export const BackgroundPanel = ({
  open,
  onOpenChange,
  onBackgroundChange,
}: BackgroundPanelProps) => {
  const [activeTab, setActiveTab] = useState<"color" | "image">("color")
  const fileInputRef = useRef<HTMLInputElement>(null)

  const predefinedColors = [
    "#ffffff",
    "#f8f9fa",
    "#e9ecef",
    "#dee2e6",
    "#000000",
    "#343a40",
    "#495057",
    "#6c757d",
    "#007bff",
    "#0056b3",
    "#17a2b8",
    "#138496",
    "#28a745",
    "#1e7e34",
    "#ffc107",
    "#e0a800",
    "#dc3545",
    "#c82333",
    "#6f42c1",
    "#563d7c",
    "#e83e8c",
    "#c2185b",
    "#fd7e14",
    "#d39e00",
  ]

  const backgroundImages = [
    "https://images.unsplash.com/photo-1557683316-973673baf926?w=800",
    "https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=800",
    "https://images.unsplash.com/photo-1557682250-33bd709cbe85?w=800",
    "https://images.unsplash.com/photo-1557683304-673a23048d34?w=800",
    "https://images.unsplash.com/photo-1557804506-669a67965ba0?w=800",
    "https://images.unsplash.com/photo-1558618047-3c8c76ca7d13?w=800",
  ]

  // 处理文件上传
  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files
    if (!files || files.length === 0) return

    const file = files[0]
    const reader = new FileReader()

    reader.onload = (e) => {
      const dataUrl = e.target?.result as string
      if (dataUrl) {
        onBackgroundChange("image", dataUrl)
      }
    }

    reader.readAsDataURL(file)

    // 清空文件输入
    if (fileInputRef.current) {
      fileInputRef.current.value = ""
    }
  }

  return (
    <Drawer.Root open={open} onOpenChange={onOpenChange}>
      <Drawer.Portal>
        <Drawer.Overlay className="fixed inset-0 bg-black/40" />
        <Drawer.Content className="bg-white h-[70vh] fixed bottom-0 left-0 right-0 outline-none rounded-t-xl">
          <div className="flex flex-col h-full">
            {/* 头部 */}
            <div className="flex items-center justify-between p-4 border-b">
              <h2 className="text-lg font-semibold">背景设置</h2>
              <button
                type="button"
                onClick={() => onOpenChange(false)}
                className="p-1 hover:bg-gray-100 rounded"
              >
                <X size={20} />
              </button>
            </div>

            {/* 标签页 */}
            <div className="flex border-b">
              <button
                type="button"
                onClick={() => setActiveTab("color")}
                className={`flex-1 flex items-center justify-center py-3 space-x-2 ${
                  activeTab === "color"
                    ? "border-b-2 border-blue-500 text-blue-500"
                    : "text-gray-600"
                }`}
              >
                <Palette size={20} />
                <span>纯色</span>
              </button>
              <button
                type="button"
                onClick={() => setActiveTab("image")}
                className={`flex-1 flex items-center justify-center py-3 space-x-2 ${
                  activeTab === "image"
                    ? "border-b-2 border-blue-500 text-blue-500"
                    : "text-gray-600"
                }`}
              >
                <ImageIcon size={20} />
                <span>图片</span>
              </button>
            </div>

            {/* 内容区 */}
            <div className="flex-1 p-4 overflow-y-auto">
              {activeTab === "color" ? (
                <div className="space-y-4">
                  <div>
                    <h3 className="font-medium mb-2">选择颜色</h3>
                    <ColorPicker
                      title=""
                      defaultColor="#ffffff"
                      onChange={(color) => onBackgroundChange("color", color)}
                    />
                  </div>

                  <div>
                    <h3 className="font-medium mb-2">预设颜色</h3>
                    <div className="grid grid-cols-8 gap-2">
                      {predefinedColors.map((color, index) => (
                        <button
                          type="button"
                          key={index}
                          onClick={() => onBackgroundChange("color", color)}
                          className="w-8 h-8 rounded border border-gray-200 hover:scale-110 transition-transform"
                          style={{ backgroundColor: color }}
                        />
                      ))}
                    </div>
                  </div>
                </div>
              ) : (
                <div className="space-y-4">
                  {/* 文件上传 */}
                  <div>
                    <h3 className="font-medium mb-2">上传图片</h3>
                    <input
                      type="file"
                      ref={fileInputRef}
                      style={{ display: "none" }}
                      accept="image/*"
                      onChange={handleFileUpload}
                    />
                    <Button
                      onClick={() => fileInputRef.current?.click()}
                      variant="outline"
                      className="w-full"
                    >
                      <Upload size={16} className="mr-2" />
                      选择图片文件
                    </Button>
                  </div>

                  {/* 预设背景图片 */}
                  <div>
                    <h3 className="font-medium mb-2">预设背景</h3>
                    <div className="grid grid-cols-2 gap-3">
                      {backgroundImages.map((image, index) => (
                        <button
                          key={index}
                          onClick={() => onBackgroundChange("image", image)}
                          className="aspect-video rounded-lg overflow-hidden border border-gray-200 hover:border-blue-500 transition-colors"
                        >
                          <img
                            src={image}
                            alt={`背景图片 ${index + 1}`}
                            className="w-full h-full object-cover"
                          />
                        </button>
                      ))}
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
        </Drawer.Content>
      </Drawer.Portal>
    </Drawer.Root>
  )
}
