import type <PERSON><PERSON><PERSON> from "@/components/pikaso"
import type { LabelModel } from "@/components/pikaso/shape/models/LabelModel"
import type { TextModel } from "@/components/pikaso/shape/models/TextModel"
import {
  <PERSON><PERSON>,
  DialogContent,
  <PERSON>alogFooter,
  <PERSON><PERSON>Header,
  <PERSON>alogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"

import type Konva from "konva"
import {
  AlignCenter,
  AlignLeft,
  AlignRight,
  Circle,
  Minus,
  Palette,
  Plus,
  Square,
  Trash2,
  Type,
  XCircle,
} from "lucide-react"
import type React from "react"
import { useState } from "react"

// 定义可用的字体列表
const FONT_FAMILIES = [
  "Arial",
  "Helvetica",
  "Times New Roman",
  "Courier New",
  "Georgia",
  "Verdana",
  "Impact",
]

// Define SelectedTextToolbar component
export interface SelectedTextToolbarProps {
  editor: Pikaso | null
  selectedShape: TextModel | LabelModel
  onDelete: () => void
  onChangeText: (newText: string) => void
  onChangeFontSize: (delta: number) => void
  onChangeColor: (color: string) => void
  onEditText: () => void
  isMobile: boolean
}

export const SelectedTextToolbar: React.FC<SelectedTextToolbarProps> = ({
  editor,
  selectedShape,
  onDelete,
  onChangeText,
  onChangeFontSize,
  onChangeColor,
  onEditText,
  isMobile,
}) => {
  // 检查selectedShape是否为LabelModel
  const isLabelModel =
    selectedShape.type === "label" && "updateTag" in selectedShape

  const handleClose = () => {
    if (editor?.board?.selection) {
      editor.board.selection.deselectAll()
    }
  }

  const buttonClass =
    "flex flex-col items-center text-white hover:bg-gray-700 p-1 md:p-2 rounded focus:outline-none focus:ring-2 focus:ring-indigo-500"
  const iconSize = isMobile ? 18 : 22
  const textSize = isMobile ? "text-[10px]" : "text-xs"

  // 获取文本节点
  const getTextNode = () => {
    if (isLabelModel) {
      return (selectedShape as LabelModel).textNode
    }
    return selectedShape.node as Konva.Text
  }

  const handleColorChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    onChangeColor(e.target.value)
  }

  // 处理背景颜色变化
  const handleBackgroundColorChange = (
    e: React.ChangeEvent<HTMLInputElement>,
  ) => {
    if (isLabelModel) {
      ;(selectedShape as LabelModel).updateTag({ fill: e.target.value })
    }
  }

  // 处理圆角变化
  const handleCornerRadiusChange = (radius: number) => {
    if (isLabelModel) {
      ;(selectedShape as LabelModel).updateTag({ cornerRadius: radius })
    }
  }

  // 处理文本对齐
  const handleTextAlign = (align: string) => {
    if (isLabelModel) {
      ;(selectedShape as LabelModel).updateText({ align })
    } else {
      selectedShape.update({ align })
    }
  }

  // 处理字体变化
  const handleFontFamilyChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    if (isLabelModel) {
      ;(selectedShape as LabelModel).updateText({ fontFamily: e.target.value })
    } else {
      selectedShape.update({ fontFamily: e.target.value })
    }
  }

  // State for inline text editing
  const [isEditingText, setIsEditingText] = useState(false)
  const [textContent, setTextContent] = useState(getTextNode().text())

  // 获取当前文本对齐方式
  const getCurrentAlign = () => {
    return getTextNode().align() || "left"
  }

  // 获取当前字体
  const getCurrentFontFamily = () => {
    return getTextNode().fontFamily() || "Arial"
  }

  // 获取当前背景颜色
  const getCurrentBackgroundColor = () => {
    if (isLabelModel) {
      const tagNode = (selectedShape as LabelModel).tagNode
      return typeof tagNode.getAttr("fill") === "string"
        ? (tagNode.getAttr("fill") as string)
        : "transparent"
    }
    return "transparent"
  }

  const handleTextChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setTextContent(e.target.value)
  }

  const handleTextBlur = () => {
    onChangeText(textContent)
    setIsEditingText(false)
  }

  // 显示更多选项的状态
  const [showMoreOptions, setShowMoreOptions] = useState(false)

  return (
    <div className="bg-gray-800 p-1 md:p-2 flex flex-col shadow-md">
      <div className="flex justify-between items-center">
        <div className="flex justify-start items-center space-x-1 md:space-x-2 overflow-x-auto">
          <button
            type="button"
            onClick={onDelete}
            className={buttonClass}
            title="删除"
          >
            <Trash2 size={iconSize} />
            <span className={`${textSize} mt-1`}>删除</span>
          </button>
          <Dialog open={isEditingText} onOpenChange={setIsEditingText}>
            <DialogTrigger asChild>
              <button
                type="button"
                onClick={() => setIsEditingText(true)}
                className={buttonClass}
                title="编辑文本"
              >
                <Type size={iconSize} />
                <span className={`${textSize} mt-1`}>编辑</span>
              </button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>编辑文本</DialogTitle>
              </DialogHeader>
              <textarea
                value={textContent}
                onChange={handleTextChange}
                className="w-full h-32 p-2 text-sm text-black rounded border border-gray-600 my-4 resize-none focus:outline-none focus:ring-2 focus:ring-indigo-500"
                placeholder="输入文本..."
              />
              <DialogFooter>
                <button
                  type="button"
                  onClick={handleTextBlur}
                  className="bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded focus:outline-none focus:ring-2 focus:ring-indigo-500"
                >
                  确定
                </button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
          <button
            type="button"
            onClick={() => onChangeFontSize(2)}
            className={buttonClass}
            title="增大字号"
          >
            <Plus size={iconSize} />
            <span className={`${textSize} mt-1`}>增大</span>
          </button>
          <button
            type="button"
            onClick={() => onChangeFontSize(-2)}
            className={buttonClass}
            title="减小字号"
          >
            <Minus size={iconSize} />
            <span className={`${textSize} mt-1`}>减小</span>
          </button>
          <div className={buttonClass} title="文本颜色">
            <Palette size={iconSize} />
            <input
              type="color"
              value={
                typeof getTextNode().getAttr("fill") === "string"
                  ? (getTextNode().getAttr("fill") as string)
                  : "#000000"
              }
              onChange={handleColorChange}
              className="w-6 h-6 md:w-8 md:h-8 opacity-0 absolute cursor-pointer"
            />
            <span className={`${textSize} mt-1`}>文本色</span>
          </div>

          {/* 文本对齐按钮 */}
          <div className="flex flex-col items-center">
            <div className="flex space-x-1">
              <button
                type="button"
                onClick={() => handleTextAlign("left")}
                className={`p-1 rounded ${getCurrentAlign() === "left" ? "bg-indigo-600" : "hover:bg-gray-700"}`}
                title="左对齐"
              >
                <AlignLeft size={iconSize} color="white" />
              </button>
              <button
                type="button"
                onClick={() => handleTextAlign("center")}
                className={`p-1 rounded ${getCurrentAlign() === "center" ? "bg-indigo-600" : "hover:bg-gray-700"}`}
                title="居中"
              >
                <AlignCenter size={iconSize} color="white" />
              </button>
              <button
                type="button"
                onClick={() => handleTextAlign("right")}
                className={`p-1 rounded ${getCurrentAlign() === "right" ? "bg-indigo-600" : "hover:bg-gray-700"}`}
                title="右对齐"
              >
                <AlignRight size={iconSize} color="white" />
              </button>
            </div>
            <span className={`${textSize} mt-1`}>对齐</span>
          </div>

          <button
            type="button"
            onClick={() => setShowMoreOptions(!showMoreOptions)}
            className={`${buttonClass} ${showMoreOptions ? "bg-indigo-600" : ""}`}
            title={showMoreOptions ? "收起选项" : "更多选项"}
          >
            <span className={`${textSize} mt-1`}>
              {showMoreOptions ? "收起" : "更多"}
            </span>
          </button>
        </div>
        <button
          type="button"
          onClick={handleClose}
          className="text-gray-300 hover:text-white p-1 md:p-2 rounded ml-2 md:ml-4 flex items-center focus:outline-none focus:ring-2 focus:ring-indigo-500"
          title="关闭工具栏"
        >
          <XCircle size={isMobile ? 16 : 18} />
          <span className={`${isMobile ? "hidden" : "inline"} ml-1 text-xs`}>
            关闭
          </span>
        </button>
      </div>

      {/* 更多选项面板 */}
      {showMoreOptions && (
        <div className="mt-2 pt-2 border-t border-gray-700 flex flex-wrap gap-2">
          {/* 字体选择 */}
          <div className="flex flex-col items-start">
            <label
              htmlFor="font-select"
              className={`${textSize} text-white mb-1`}
            >
              字体
            </label>
            <select
              id="font-select"
              value={getCurrentFontFamily()}
              onChange={handleFontFamilyChange}
              className="bg-gray-700 text-white text-sm rounded p-1 focus:outline-none focus:ring-2 focus:ring-indigo-500"
            >
              {FONT_FAMILIES.map((font) => (
                <option key={font} value={font}>
                  {font}
                </option>
              ))}
            </select>
          </div>

          {/* 仅对LabelModel显示的选项 */}
          {isLabelModel && (
            <>
              {/* 背景颜色 */}
              <div className="flex flex-col items-start">
                <label
                  htmlFor="bg-color-input"
                  className={`${textSize} text-white mb-1`}
                >
                  背景颜色
                </label>
                <div className="flex items-center bg-gray-700 rounded p-1">
                  <input
                    id="bg-color-input"
                    type="color"
                    value={getCurrentBackgroundColor()}
                    onChange={handleBackgroundColorChange}
                    className="w-6 h-6 cursor-pointer border-0"
                  />
                  <span className="text-white text-xs ml-1">
                    {getCurrentBackgroundColor()}
                  </span>
                </div>
              </div>

              {/* 圆角设置 */}
              <div className="flex flex-col items-start">
                <label
                  htmlFor="corner-radius-buttons"
                  className={`${textSize} text-white mb-1`}
                >
                  边框圆角
                </label>
                <div id="corner-radius-buttons" className="flex space-x-2">
                  <button
                    type="button"
                    onClick={() => handleCornerRadiusChange(0)}
                    className="bg-gray-700 hover:bg-gray-600 p-1 rounded"
                    title="无圆角"
                  >
                    <Square size={iconSize - 4} color="white" />
                  </button>
                  <button
                    type="button"
                    onClick={() => handleCornerRadiusChange(4)}
                    className="bg-gray-700 hover:bg-gray-600 p-1 rounded"
                    title="小圆角"
                  >
                    <Square
                      size={iconSize - 4}
                      color="white"
                      className="rounded-sm"
                    />
                  </button>
                  <button
                    type="button"
                    onClick={() => handleCornerRadiusChange(8)}
                    className="bg-gray-700 hover:bg-gray-600 p-1 rounded"
                    title="中圆角"
                  >
                    <Square
                      size={iconSize - 4}
                      color="white"
                      className="rounded-md"
                    />
                  </button>
                  <button
                    type="button"
                    onClick={() => handleCornerRadiusChange(16)}
                    className="bg-gray-700 hover:bg-gray-600 p-1 rounded"
                    title="大圆角"
                  >
                    <Square
                      size={iconSize - 4}
                      color="white"
                      className="rounded-lg"
                    />
                  </button>
                  <button
                    type="button"
                    onClick={() => handleCornerRadiusChange(999)}
                    className="bg-gray-700 hover:bg-gray-600 p-1 rounded"
                    title="完全圆角"
                  >
                    <Circle size={iconSize - 4} color="white" />
                  </button>
                </div>
              </div>
            </>
          )}
        </div>
      )}
    </div>
  )
}
