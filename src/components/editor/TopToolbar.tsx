import { ExportBoard } from "@/components/ExportBoard"
import { ImportBoard } from "@/components/ImportBoard"
import type { <PERSON><PERSON><PERSON> } from "@/components/pikaso/index.all"
import { Button } from "@/components/ui/button"
import { Eye, Redo, Save, Undo } from "lucide-react"

interface TopToolbarProps {
  editor: Pikaso | null
  onSave?: () => void
  onPreview?: () => void
}

export const TopToolbar = ({ editor, onSave, onPreview }: TopToolbarProps) => {
  return (
    <div className="flex justify-between items-center px-4 py-2 bg-white border-b border-gray-200">
      {/* Left side container */}
      <div className="flex items-center space-x-1 sm:space-x-2">
        {/* Saved Status - Desktop only */}
        <div className="hidden sm:flex items-center text-green-600 mr-4">
          <Save size={18} className="mr-2" />
          <span className="text-sm">已保存</span>
        </div>

        {/* Undo/Redo */}
        <div className="flex items-center space-x-1">
          <Button
            size="sm"
            variant="outline"
            onClick={() => editor?.undo()}
            disabled={!editor}
            className="min-w-0 px-2"
          >
            <Undo size={16} className="sm:mr-1" />
            <span className="hidden sm:inline">撤销</span>
          </Button>
          <Button
            size="sm"
            variant="outline"
            onClick={() => editor?.redo()}
            disabled={!editor}
            className="min-w-0 px-2"
          >
            <Redo size={16} className="sm:mr-1" />
            <span className="hidden sm:inline">重做</span>
          </Button>
        </div>

        {/* Import/Export - Desktop only */}
        <div className="hidden sm:flex items-center space-x-1 ml-4">
          <ImportBoard editor={editor} />
          <ExportBoard editor={editor} />
        </div>
      </div>

      {/* Center - Saved Status on Mobile */}
      <div className="sm:hidden flex items-center text-green-600">
        <Save size={16} className="mr-1" />
        <span className="text-sm">已保存</span>
      </div>

      {/* Right side container */}
      <div className="flex items-center space-x-1 sm:space-x-2">
        {/* Preview Button - Mobile */}
        <Button
          size="sm"
          variant="outline"
          onClick={onPreview}
          className="sm:hidden"
        >
          <Eye size={16} />
        </Button>
        {/* Preview Button - Desktop */}
        <Button
          size="sm"
          variant="default"
          onClick={onPreview}
          className="hidden sm:flex"
        >
          <Eye size={16} className="mr-1" />
          <span>预览</span>
        </Button>

        {/* Save Button - Combined */}
        <Button
          size="sm"
          variant="default"
          className="bg-green-600 hover:bg-green-700"
          onClick={onSave}
        >
          <Save size={16} className="sm:mr-1" />
          <span className="hidden sm:inline">保存</span>
        </Button>
      </div>
    </div>
  )
}
