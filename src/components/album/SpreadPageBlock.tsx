import { Button } from "@/components/ui/button"
import type { Page } from "@/types/album"
import { useSortable } from "@dnd-kit/sortable"
import { CSS } from "@dnd-kit/utilities"
import { Edit3, Menu, Plus, Trash2 } from "lucide-react"
import PageThumbnail from "./PageThumbnail"
import { calculateDisplayPageNumber } from "./utils"

interface SpreadPageBlockProps {
  leftPage: Page | null
  rightPage: Page
  spreadIndex: number
  onEdit: (id: string) => void
  onDelete: (id: string) => void
  onInsertAfter: (pageId: string) => void
  isDragging: boolean
}

export function SpreadPageBlock({
  leftPage,
  rightPage,
  spreadIndex,
  onEdit,
  onDelete,
  onInsertAfter,
  isDragging,
}: SpreadPageBlockProps) {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging: isSortableDragging,
  } = useSortable({ id: rightPage.id })

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
  }

  // 检查是否为第一个展开页（第一个内容页）
  const isFirstSpread = spreadIndex === 0

  return (
    <div
      ref={setNodeRef}
      style={style}
      className={`bg-white rounded-xl shadow-sm border overflow-hidden hover:shadow-md transition-all duration-200 ${
        isSortableDragging
          ? "shadow-lg ring-2 ring-purple-500 scale-105"
          : "border-gray-200"
      }`}
    >
      <div className="relative">
        <div
          className="absolute top-3 left-3 z-10"
          {...attributes}
          {...listeners}
        >
          <Button
            variant="ghost"
            size="sm"
            className={`p-1.5 bg-white/90 backdrop-blur-sm rounded-md shadow-sm hover:bg-white transition-all ${
              isSortableDragging
                ? "cursor-grabbing scale-110"
                : isDragging
                  ? "cursor-grab ring-2 ring-purple-300 animate-pulse"
                  : "cursor-grab"
            }`}
          >
            <Menu size={14} className="text-gray-600" />
          </Button>
        </div>

        <button
          type="button"
          className="w-full bg-gray-50 p-3 cursor-pointer hover:bg-gray-100 transition-colors relative"
          onClick={() => onEdit(rightPage.id)}
        >
          <div className="grid grid-cols-2 gap-1.5">
            {/* 左页 */}
            <div className="relative aspect-[1/1] bg-white rounded border border-gray-200 overflow-hidden">
              {leftPage ? (
                <>
                  <PageThumbnail page={leftPage} />
                  {calculateDisplayPageNumber(leftPage, spreadIndex, true) && (
                    <div className="absolute bottom-1 right-1 text-xs font-medium text-gray-500 bg-white/80 px-1 rounded">
                      {calculateDisplayPageNumber(leftPage, spreadIndex, true)}
                    </div>
                  )}
                </>
              ) : (
                <div className="flex items-center justify-center h-full bg-gray-50">
                  <span className="text-gray-400 text-xs">空白页</span>
                </div>
              )}
            </div>
            {/* 右页 */}
            <div className="relative aspect-[1/1] bg-white rounded border border-gray-200 overflow-hidden">
              <PageThumbnail page={rightPage} />
              {calculateDisplayPageNumber(rightPage, spreadIndex, false) && (
                <div className="absolute bottom-1 right-1 text-xs font-medium text-gray-500 bg-white/80 px-1 rounded">
                  {calculateDisplayPageNumber(rightPage, spreadIndex, false)}
                </div>
              )}
            </div>
          </div>
        </button>
      </div>

      <div className="p-4 bg-gray-50/50">
        <div className="flex justify-center space-x-4">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => onInsertAfter(rightPage.id)}
            className="p-2 text-purple-600 hover:bg-purple-50 rounded-full transition-colors"
            title="在此页后插入新页面"
          >
            <Plus size={16} />
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => onEdit(rightPage.id)}
            className="p-2 text-blue-600 hover:bg-blue-50 rounded-full transition-colors"
            title="编辑页面"
          >
            <Edit3 size={16} />
          </Button>
          {!isFirstSpread && (
            <Button
              variant="ghost"
              size="sm"
              onClick={() => onDelete(rightPage.id)}
              className="p-2 text-red-500 hover:bg-red-50 rounded-full transition-colors"
              title="删除页面"
            >
              <Trash2 size={16} />
            </Button>
          )}
        </div>
      </div>
    </div>
  )
}
